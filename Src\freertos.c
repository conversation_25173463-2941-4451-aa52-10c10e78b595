/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * File Name          : freertos.c
  * Description        : Code for freertos applications
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "FreeRTOS.h"
#include "task.h"
#include "main.h"
#include "cmsis_os.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN Variables */

/* USER CODE END Variables */
osThreadId GPSTaskHandle;
uint32_t GPSTaskBuffer[ 512 ];
osStaticThreadDef_t GPSTaskControlBlock;
osThreadId AccelTaskHandle;
uint32_t AccelTaskBuffer[ 512 ];
osStaticThreadDef_t AccelTaskControlBlock;
osThreadId GSMTaskHandle;
uint32_t GSMTaskBuffer[ 512 ];
osStaticThreadDef_t GSMTaskControlBlock;
osThreadId FlashTaskHandle;
uint32_t FlashTaskBuffer[ 128 ];
osStaticThreadDef_t FlashTaskControlBlock;
osThreadId myPowerTaskHandle;
uint32_t myPowerTaskBuffer[ 512 ];
osStaticThreadDef_t myPowerTaskControlBlock;

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN FunctionPrototypes */

/* USER CODE END FunctionPrototypes */

void StartGPSTask(void const * argument);
void StartAccelTask(void const * argument);
void StartGSMTask(void const * argument);
void StartFlashTask(void const * argument);
void StartPowerTask(void const * argument);

void MX_FREERTOS_Init(void); /* (MISRA C 2004 rule 8.1) */

/* GetIdleTaskMemory prototype (linked to static allocation support) */
void vApplicationGetIdleTaskMemory( StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize );

/* USER CODE BEGIN GET_IDLE_TASK_MEMORY */
static StaticTask_t xIdleTaskTCBBuffer;
static StackType_t xIdleStack[configMINIMAL_STACK_SIZE];

void vApplicationGetIdleTaskMemory( StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize )
{
  *ppxIdleTaskTCBBuffer = &xIdleTaskTCBBuffer;
  *ppxIdleTaskStackBuffer = &xIdleStack[0];
  *pulIdleTaskStackSize = configMINIMAL_STACK_SIZE;
  /* place for user code */
}
/* USER CODE END GET_IDLE_TASK_MEMORY */

/**
  * @brief  FreeRTOS initialization
  * @param  None
  * @retval None
  */
void MX_FREERTOS_Init(void) {
  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* USER CODE BEGIN RTOS_MUTEX */
  /* add mutexes, ... */
  /* USER CODE END RTOS_MUTEX */

  /* USER CODE BEGIN RTOS_SEMAPHORES */
  /* add semaphores, ... */
  /* USER CODE END RTOS_SEMAPHORES */

  /* USER CODE BEGIN RTOS_TIMERS */
  /* start timers, add new ones, ... */
  /* USER CODE END RTOS_TIMERS */

  /* USER CODE BEGIN RTOS_QUEUES */
  /* add queues, ... */
  /* USER CODE END RTOS_QUEUES */

  /* Create the thread(s) */
  /* definition and creation of GPSTask */
  osThreadStaticDef(GPSTask, StartGPSTask, osPriorityNormal, 0, 512, GPSTaskBuffer, &GPSTaskControlBlock);
  GPSTaskHandle = osThreadCreate(osThread(GPSTask), NULL);

  /* definition and creation of AccelTask */
  osThreadStaticDef(AccelTask, StartAccelTask, osPriorityIdle, 0, 512, AccelTaskBuffer, &AccelTaskControlBlock);
  AccelTaskHandle = osThreadCreate(osThread(AccelTask), NULL);

  /* definition and creation of GSMTask */
  osThreadStaticDef(GSMTask, StartGSMTask, osPriorityIdle, 0, 512, GSMTaskBuffer, &GSMTaskControlBlock);
  GSMTaskHandle = osThreadCreate(osThread(GSMTask), NULL);

  /* definition and creation of FlashTask */
  osThreadStaticDef(FlashTask, StartFlashTask, osPriorityIdle, 0, 128, FlashTaskBuffer, &FlashTaskControlBlock);
  FlashTaskHandle = osThreadCreate(osThread(FlashTask), NULL);

  /* definition and creation of myPowerTask */
  osThreadStaticDef(myPowerTask, StartPowerTask, osPriorityIdle, 0, 512, myPowerTaskBuffer, &myPowerTaskControlBlock);
  myPowerTaskHandle = osThreadCreate(osThread(myPowerTask), NULL);

  /* USER CODE BEGIN RTOS_THREADS */
  /* add threads, ... */
  /* USER CODE END RTOS_THREADS */

}

/* USER CODE BEGIN Header_StartGPSTask */
/**
  * @brief  Function implementing the GPSTask thread.
  * @param  argument: Not used
  * @retval None
  */
/* USER CODE END Header_StartGPSTask */
void StartGPSTask(void const * argument)
{
  /* USER CODE BEGIN StartGPSTask */
  /* Infinite loop */
  for(;;)
  {
    osDelay(1);
  }
  /* USER CODE END StartGPSTask */
}

/* USER CODE BEGIN Header_StartAccelTask */
/**
* @brief Function implementing the AccelTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartAccelTask */
void StartAccelTask(void const * argument)
{
  /* USER CODE BEGIN StartAccelTask */
  /* Infinite loop */
  for(;;)
  {
    osDelay(1);
  }
  /* USER CODE END StartAccelTask */
}

/* USER CODE BEGIN Header_StartGSMTask */
/**
* @brief Function implementing the GSMTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartGSMTask */
void StartGSMTask(void const * argument)
{
  /* USER CODE BEGIN StartGSMTask */
  /* Infinite loop */
  for(;;)
  {
    osDelay(1);
  }
  /* USER CODE END StartGSMTask */
}

/* USER CODE BEGIN Header_StartFlashTask */
/**
* @brief Function implementing the FlashTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartFlashTask */
void StartFlashTask(void const * argument)
{
  /* USER CODE BEGIN StartFlashTask */
  /* Infinite loop */
  for(;;)
  {
    osDelay(1);
  }
  /* USER CODE END StartFlashTask */
}

/* USER CODE BEGIN Header_StartPowerTask */
/**
* @brief Function implementing the myPowerTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartPowerTask */
void StartPowerTask(void const * argument)
{
  /* USER CODE BEGIN StartPowerTask */
  /* Infinite loop */
  for(;;)
  {
    osDelay(1);
  }
  /* USER CODE END StartPowerTask */
}

/* Private application code --------------------------------------------------*/
/* USER CODE BEGIN Application */

/* USER CODE END Application */

