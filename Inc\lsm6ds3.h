#ifndef LSM6DS3_H
#define LSM6DS3_H

#include "main.h"

#define LSM6DS3_ADDR_0       (0x6A << 1)  // SDO/SA0 = GND
#define LSM6DS3_ADDR_1       (0x6B << 1)  // SDO/SA0 = VDD
#define LSM6DS3_ADDR         LSM6DS3_ADDR_0  // 默认使用地址0

#define WHO_AM_I_REG         0x0F
#define CTRL1_XL             0x10
#define CTRL2_G              0x11
#define CTRL3_C              0x12
#define CTRL4_C              0x13
#define CTRL5_C              0x14
#define CTRL6_C              0x15
#define CTRL7_G              0x16
#define CTRL8_XL             0x17
#define CTRL9_XL             0x18
#define CTRL10_C             0x19
#define WAKE_UP_SRC          0x1B
#define TAP_SRC              0x1C
#define D6D_SRC              0x1D
#define STATUS_REG           0x1E
#define OUTX_L_G             0x22
#define OUTX_L_XL            0x28
#define OUT_TEMP_L           0x20



// 互补滤波器参数
#define COMP_FILTER_ALPHA    0.8f  // 互补滤波器系数，可根据需要调整
#define SAMPLE_FREQ          104.0f  // 采样频率，与传感器配置一致
#define SAMPLE_TIME          (1.0f / SAMPLE_FREQ)

typedef struct {
    float ax, ay, az;
    float gx, gy, gz;
    float temp_celsius;
} LSM6DS3_Data;

// 姿态角结构体
typedef struct {
    float pitch;    // 俯仰角
    float roll;     // 横滚角
    float yaw;      // 偏航角（由于没有磁力计，此值不准确）
    float lastPitch; // 上一次的俯仰角
    float lastRoll;  // 上一次的横滚角
    float lastYaw;   // 上一次的偏航角
    uint32_t lastUpdateTime; // 上次更新时间
    uint8_t initialized;     // 是否已初始化
} LSM6DS3_Attitude;

void LSM6DS3_Init(I2C_HandleTypeDef *hi2c);
void LSM6DS3_ReadData(I2C_HandleTypeDef *hi2c, LSM6DS3_Data *data);

// 寄存器读取函数
void ReadRegs(I2C_HandleTypeDef *hi2c, uint8_t reg, uint8_t *buf, uint8_t len);

// 基本姿态角计算函数
float LSM6DS3_GetPitch(const LSM6DS3_Data *data);
float LSM6DS3_GetRoll(const LSM6DS3_Data *data);

// 互补滤波相关函数
void LSM6DS3_InitAttitude(LSM6DS3_Attitude *attitude);
void LSM6DS3_ComplementaryFilter(LSM6DS3_Data *data, LSM6DS3_Attitude *attitude);

#endif
